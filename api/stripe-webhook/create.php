<?php

$payload = @file_get_contents("php://input");
global $api_url;
global $slack_bearer_token;
global $slack_channel;

try {
    $event = \Stripe\Webhook::constructEvent(
        $payload,
        $_SERVER["HTTP_STRIPE_SIGNATURE"],
        $endpoint_secret,
    );
    // --------------------------- [customer.created] --------------------------- //
    // UPDATE Donors table with stripe customer id
    if ($event->type == "customer.created") {
        $customer_id = $event->data->object->id; // VARCHAR(255)
        $donor_id = $event->data->object->metadata->donor_id;

        // UPDATE donor
        $stmt = $db->prepare("UPDATE `donors`
         SET
             `stripe_cus_id` = :customer_id
         WHERE
            donors.id=:donor_id AND `stripe_cus_id` is NULL;");
        $stmt->execute([
            "customer_id" => $customer_id,
            "donor_id" => $donor_id,
        ]);

        // echo Donor Object
        echo json_encode(
            (new Donor($db))->read($donor_id)->fetch(PDO::FETCH_ASSOC),
            JSON_NUMERIC_CHECK,
        );
    }

    // --------------------------- [customer.updated] --------------------------- //
    if ($event->type == "customer.updated") {
        // UPDATE donor details with latest info from Stripe.
        // TODO: also update shipping info on latest shipment?
        // set stripe_cus_id
        $stripe_cus_id = $event->data->object->id;
        // instantiate Donor class
        $donor = new Donor($db);
        // set stripe_cus_id object on Donor Class for lookup
        $donor->stripe_cus_id = $stripe_cus_id;
        // lookup donor_id by stripe_cus_id and assign to donor object
        $donor = $donor->searchDonor()->fetch(PDO::FETCH_ASSOC);

        // lookup donor by stripe_cus_id
        if (!empty($donor["donor_id"])) {
            // set donor_id
            $donor_id = $donor["donor_id"];

            // lookup email
            if (!empty($event->data->object->email)) {
                $key = "email";
                $value = $event->data->object->email;

                // check for validity
                $email_domain = substr(strrchr($value, "@"), 1);
                if (checkdnsrr($email_domain, "MX")) {
                    // success
                    (new Donor($db))->update($donor_id, $key, $value); // call update donor function
                } else {
                    // failure
                    http_response_code(400); // Bad Request
                    throw new Exception(
                        "[email] address $value is undeliverable",
                    );
                }
            }
            // lookup phone
            if (!empty($event->data->object->phone)) {
                $key = "phone";
                $value = $event->data->object->phone;
                (new Donor($db))->update($donor_id, $key, $value); // call update donor function
            }
            // lookup address1
            if (!empty($event->data->object->address->line1)) {
                $key = "address1";
                $value = $event->data->object->address->line1;
                (new Donor($db))->update($donor_id, $key, $value); // call update donor function
            }
            // lookup address2
            if (!empty($event->data->object->address->line2)) {
                $key = "address2";
                $value = $event->data->object->address->line2;
                (new Donor($db))->update($donor_id, $key, $value); // call update donor function
            } else {
                $key = "address2";
                $value = null;
                (new Donor($db))->update($donor_id, $key, $value); // call update donor function
            }
            // lookup city
            if (!empty($event->data->object->address->city)) {
                $key = "city";
                $value = $event->data->object->address->city;
                (new Donor($db))->update($donor_id, $key, $value); // call update donor function
            }
            // lookup postal_code
            if (!empty($event->data->object->address->postal_code)) {
                $key = "postal_code";
                $value = $event->data->object->address->postal_code;
                (new Donor($db))->update($donor_id, $key, $value); // call update donor function
            }
            // lookup country
            if (!empty($event->data->object->address->country)) {
                $key = "country";
                $value = $event->data->object->address->country;
                (new Donor($db))->update($donor_id, $key, $value); // call update donor function
            }
            // lookup state
            if (!empty($event->data->object->address->state)) {
                $key = "state";
                $value = $event->data->object->address->state;
                (new Donor($db))->update($donor_id, $key, $value); // call update donor function
            }
            // update shipping info...
            if (!empty($event->data->object->shipping->address->state)) {
                $key = "state";
                $value = $event->data->object->shipping->address->state;
                (new Donor($db))->update($donor_id, $key, $value); // call update donor function
            }
        } else {
            // http failure
            http_response_code(404);
            throw new Exception("[donor_id] not found for $stripe_cus_id");
        }
        // success
        http_response_code(200); // success
        // echo Donor Object
        echo json_encode(
            (new Donor($db))->read($donor_id)->fetch(PDO::FETCH_ASSOC),
            JSON_NUMERIC_CHECK,
        );
        exit();
    }

    if ($event->type == "charge.succeeded") {
        include "events/charge/succeeded.php";
    }

    if ($event->type == "charge.failed") {
        include "events/charge/failed.php";
    }

    // --------------------------- [charge.updated] --------------------------- //
    if ($event->type == "charge.updated") {
        include "events/charge/updated.php";
    }

    // --------------------------- [charge.refunded] --------------------------- //
    // Occurs when a charge is refunded, including partial refunds.
    // UPDATE payment table
    if ($event->type == "charge.refunded") {
        $customer_id = $event->data->object->customer; // VARCHAR(255)
        $payment_id = $event->data->object->id; //  VARCHAR(255)
        $amount = $event->data->object->amount / 100; // decimal(13,2)
        $amount_refunded = $event->data->object->amount_refunded / 100; // decimal(13,2)
        $processor = "Stripe"; // enum('Stripe', 'PayPal', 'P-check', 'K-check', 'cash')
        $fingerprint =
            $event->data->object->payment_method_details->card->fingerprint; // varchar (30)
        $card_type =
            $event->data->object->payment_method_details->card->funding; // credit, debit, prepaid, unknown
        $date_created = $event->data->object->created; // FROM_UNIXTIME(datetime)
        $last4 = $event->data->object->payment_method_details->card->last4; // SMALLINT(4)
        $brand = $event->data->object->payment_method_details->card->brand; // American Express, Diners Club, Discover, JCB, MasterCard, UnionPay, Visa, or Unknown.
        $exp_month =
            $event->data->object->payment_method_details->card->exp_month; // tinyint(2)
        $exp_year =
            $event->data->object->payment_method_details->card->exp_year; // SMALLINT(4)
        $status = $event->data->object->status; // succeeded, pending, or failed.
        $method = $event->data->object->payment_method_details->type; // 'card','check','cash','stock','inkind','bank_account'

        // UPDATE Payment record

        // UPDATE payments
        $stmt = $db->prepare("UPDATE `payments`
        SET
            `amount_refunded`=:amount_refunded,
            `status`=:status
        WHERE
            payment_id=:payment_id;");

        $stmt->execute([
            "amount_refunded" => $amount_refunded,
            "status" => $status,
            "payment_id" => $payment_id,
        ]);

        // success
        echo json_encode([
            "message" => "Payment record for refunded payment $payment_id updated successfully.",
        ]);
        exit();
    }

    // --------------------------- [customer.subscription.created] --------------------------- //
    // INSERT subscription the subscriptions table, use transaction_id from metadadta
    // happens before charge.succeeded
    if ($event->type == "customer.subscription.created") {
        sleep(2); // sleep 2 seconds (wait for donation_id to be inserted)
        $processor = "Stripe"; // enum('Stripe', 'PayPal', 'P-check', 'K-check', 'cash')
        $subscription_id = $event->data->object->id; //  VARCHAR(255)
        $customer_id = $event->data->object->customer; //  VARCHAR(255)
        $donor_id = $event->data->object->items->data[0]->metadata->donor_id; // int
        $date_created = $event->data->object->created; // FROM_UNIXTIME(datetime)
        $plan_id = $event->data->object->items->data[0]->plan->id; //VARCHAR(255)
        $transaction_id =
            $event->data->object->items->data[0]->metadata->transaction_id; //VARCHAR(255)
        $amount = $event->data->object->items->data[0]->plan->amount / 100; // decimal(13,2) "500"
        $interval = $event->data->object->items->data[0]->plan->interval; // enum "month"
        $active = (bool) $event->data->object->items->data[0]->plan->active; // boolean "true"

        // INSERT into table

        $data = [
            "processor" => $processor,
            "id" => $subscription_id,
            "customer_id" => $customer_id,
            "donor_id" => $donor_id,
            "date_created" => $date_created,
            "plan_id" => $plan_id,
            "transaction_id" => $transaction_id,
            "amount" => $amount,
            "interval" => $interval,
            "active" => $active,
        ];

        $stmt = $db->prepare("INSERT IGNORE INTO `subscriptions`
                            (`processor`, `id`, `customer_id`, `donor_id`, `date_created`, `plan_id`,`transaction_id`, `amount`, `interval`, `active`)
                            VALUES
                            (:processor, :id, :customer_id, :donor_id, FROM_UNIXTIME(:date_created), :plan_id, :transaction_id, :amount, :interval, :active)");
        $stmt->execute($data);

        // success
        http_response_code(200); // success
        echo json_encode([
            "message" => "New record ($subscription_id) created successfully.",
        ]);
        exit();
    }

    // --------------------------- [customer.subscription.deleted] --------------------------- //
    // UPDATE the subscriptions table with deleted info
    // happens before charge.succeeded
    if ($event->type == "customer.subscription.deleted") {
        $subscription_id = $event->data->object->id; //  VARCHAR(255)
        $date_canceled = $event->data->object->canceled_at;

        // Update subscriptions table

        $data = [
            "id" => $subscription_id,
            "date_canceled" => $date_canceled,
        ];

        $stmt = $db->prepare("UPDATE `subscriptions`
                            SET
                            `active` = '0',
                            `date_canceled` = FROM_UNIXTIME(:date_canceled)
                            WHERE
                            id=:id");
        $stmt->execute($data);

        // success
        http_response_code(200); // success
        echo json_encode([
            "message" => "subscription updated for $subscription_id",
        ]);
        exit();
    }

    // --------------------------- [customer.subscription.updated] --------------------------- //
    if ($event->type == "customer.subscription.updated") {
        // UPDATE the subscriptions table
        $subscription_id = $event->data->object->id;
        $customer_id = $event->data->object->customer;
        $transaction_id = $event->data->object->metadata->transaction_id; //StationAdmin transaction_id
        $donor_id = $event->data->object->metadata->donor_id; //StationAdmin donor_id

        // https://stripe.com/docs/billing/subscriptions/overview
        /* Customers have about 23 hours to make a successful payment.
        The subscriptions remains in status incomplete and the invoice is open during this time.
        If the customer pays the invoice, the subscription updates to active and the invoice to paid.
        If they don’t make a payment, the subscription updates to incomplete_expired and the invoice becomes void
         */

        // we're looking to see if the subscription expired, and if so
        // delete the subscription and remove the associated donation:

        // CHECK if status = incomplete_expired
        if ($event->data->object->status == "incomplete_expired") {
            // and if so attempt to delete the donation

            // delete Subscription from DB
            (new Subscription($db))->delete($subscription_id);

            // lookup donation_id  from DB
            $donation = (new Donation($db))
                ->readDonationByTransactionID($transaction_id)
                ->fetch(PDO::FETCH_ASSOC);

            // delete donation
            (new Donation($db))->delete($donation["id"]);

            // success
            http_response_code(200); // success
            echo json_encode([
                "message" => "Donation " . $donation["id"] . " was deleted.",
            ]);
            exit();
        }

        // CHECK if status changed from incomplete to active
        if ($event->data->object->status == "active") {
            // Update subscription status in database to active
            $stmt = $db->prepare("UPDATE `subscriptions`
                                SET `active` = '1'
                                WHERE `id` = :subscription_id");
            $stmt->execute(['subscription_id' => $subscription_id]);

            // Log successful activation
            error_log("Subscription activated: " . $subscription_id);

            // success
            http_response_code(200); // success
            echo json_encode([
                "message" => "Subscription " . $subscription_id . " activated successfully.",
            ]);
            exit();
        }

        // CHECK to see if subscription was canceled
        if (!empty($event->data->object->canceled_at)) {
            $date_canceled = $event->data->object->canceled_at;
            // Update subscriptions table
            $data = [
                "id" => $subscription_id,
                "date_canceled" => $date_canceled,
            ];

            $stmt = $db->prepare("UPDATE `subscriptions`
                            SET
                            `active` = '0',
                            `date_canceled` = FROM_UNIXTIME(:date_canceled)
                            WHERE
                            id=:id");
            $stmt->execute($data);
            // success
            http_response_code(200); // success
            echo json_encode([
                "message" => "subscriptions canceled for $subscription_id",
            ]);
            exit();
        }

        // If Status changes to requires_payment_method OR requires_action
        // TODO: Not functioning
        if (
            $event->data->object->status == "requires_payment_method" ||
            $event->data->object->status == "requires_action"
        ) {
            // Send email.
            // the message
            $msg = "[customer_id]: $customer_id\n
            [transaction_id]: $transaction_id\n
            [donor_id]: $donor_id\n";

            // use wordwrap() if lines are longer than 70 characters
            $msg = wordwrap($msg, 70);

            // send email
            mail("<EMAIL>", "[Stripe] Failed Payment_intent", $msg);
            // let stripe know
            http_response_code(200); // not modified
            echo json_encode(["message" => "Email <NAME_EMAIL>"]);
        }

        http_response_code(200); // not modified
        echo json_encode([
            "message" => "Nothing to do for subscription_id: $subscription_id",
        ]);
        exit();
    }

    if ($event->type === "payment_intent.requires_action") {
        include "events/payment_intent/requires_action.php";
    }

    if ($event->type == "payment_intent.succeeded") {
        include "events/payment_intent/succeeded.php";
    }

    // --------------------------- [payment_intent.canceled] --------------------------- //
    // New UI allows for success after failure, so payment_intent.failed is not used
    if ($event->type == "payment_intent.canceled") {
        // delete the donation
        // lookup & delete the subscription, if exists

        // lookup transaction_id (only found on one-time)
        if (!empty($event->data->object->metadata->transaction_id)) {
            $transaction_id = $event->data->object->metadata->transaction_id; //StationAdmin transaction_id

            // lookup donation_id
            $donation = (new Donation($db))
                ->readDonationByTransactionID($transaction_id)
                ->fetch(PDO::FETCH_ASSOC);

            // lookup subscription
            $subscription = (new Donation($db))
                ->readSubscriptionsByTransaction_id($transaction_id)
                ->fetch(PDO::FETCH_ASSOC);

            // is there a subscription_id?
            if (!empty($subscription["subscription_id"])) {
                // delete entry in DB for subscription_id
                (new Subscription($db))->delete(
                    $subscription["subscription_id"],
                );
            }

            // call donation delete function
            (new Donation($db))->delete($donation["id"]);

            // success
            http_response_code(200); // success
            echo json_encode([
                "message" => "Donation " . $donation["id"] . " was deleted.",
            ]);
            exit();
        }

        http_response_code(200); //success
        echo json_encode(["message" => "[transaction_id] not found"]);
        exit();
    }

    if ($event->type === "invoice.payment_action_required") {
        include "events/invoice/payment_action_required.php";
    }

    if ($event->type == "invoice.payment_failed") {
        include "events/invoice/payment_failed.php";
    }

    if ($event->type == "invoice.payment_succeeded") {
        include "events/invoice/payment_succeeded.php";
    }

    if ($event->type == "invoice.paid") {
        // Set variables
        $customer_id = $event->data->object->customer; // VARCHAR(255)
        $invoice_id = $event->data->object->id; // id of invoice

        // Call readStripePayment to see if payment already exists, and if so echo & exit
        if (!empty($event->data->object->charge)) {
            if (
                (new Payment($db))
                    ->readStripePayment($event->data->object->charge)
                    ->rowCount() > 0
            ) {
                // Read the payment and send as JSON
                echo json_encode(
                    (new Payment($db))
                        ->readStripePayment($event->data->object->charge)
                        ->fetch(PDO::FETCH_ASSOC),
                    JSON_NUMERIC_CHECK,
                );
                exit();
            }
        }

        // require transaction_id (first from the invoice)
        if (
            !empty(
                $event->data->object->lines->data[0]->metadata->transaction_id
            )
        ) {
            // set transaction_id
            $transaction_id =
                $event->data->object->lines->data[0]->metadata->transaction_id; //StationAdmin transaction_id
        } elseif (
            !empty(
                $event->data->object->lines->data[0]->plan->metadata
                    ->transaction_id
            )
        ) {
            // set only if couldn't be found in invoice, search the plan
            // set transaction_id
            $transaction_id =
                $event->data->object->lines->data[0]->plan->metadata
                    ->transaction_id; //StationAdmin transaction_id
        } elseif (!empty($event->data->object->subscription)) {
            // set only if couldn't be found in plan, search the subscription table
            // set subscription_id
            $subscription_id = $event->data->object->subscription; // Stripe Subscription
            // search the subscription table for transaction_id
            $subscription = (new Subscription($db))
                ->read($subscription_id)
                ->fetch(PDO::FETCH_ASSOC);
            if ($subscription && isset($subscription["transaction_id"])) {
                $transaction_id = $subscription["transaction_id"];
            }
        }

        if (!empty($transaction_id)) {
            // Lookup donation_id from DB
            $donation = (new Donation($db))
                ->readDonationByTransactionID($transaction_id)
                ->fetch(PDO::FETCH_ASSOC);

            // look up charge from Stripe, because it has all the info we need (aside from transaction_id)
            $charge = $stripe->charges->retrieve(
                $event->data->object->charge,
                [],
            );

            // Set variables
            $customer_id = $event->data->object->customer; // VARCHAR(255)
            $amount = $event->data->object->amount_paid / 100; // decimal(13,2)
            $payment_id = $event->data->object->charge; //  VARCHAR(255)
            $processor = "Stripe"; // enum('Stripe', 'PayPal', 'P-check', 'K-check', 'cash')
            $date_created = $charge->created; // FROM_UNIXTIME(datetime)
            $status = $charge->status; // succeeded, pending, or failed.
            $method = $charge->payment_method_details->type; // 'card','check','cash','stock','inkind','bank_account','us_bank_account','epayment','link'

            // Handle payment method details based on type
            $fingerprint = null;
            $card_type = null;
            $last4 = null;
            $brand = null;
            $exp_month = null;
            $exp_year = null;

            if ($method === "us_bank_account") {
                $last4 = $charge->payment_method_details->us_bank_account->last4;
            } elseif ($method === "card" || $method === "link") {
                // For card and link payments, card details are available
                $fingerprint = $charge->payment_method_details->card->fingerprint ?? null;
                $card_type = $charge->payment_method_details->card->funding ?? null;
                $last4 = $charge->payment_method_details->card->last4 ?? null;
                $brand = $charge->payment_method_details->card->brand ?? null;
                $exp_month = $charge->payment_method_details->card->exp_month ?? null;
                $exp_year = $charge->payment_method_details->card->exp_year ?? null;
            }
            $invoice_number = $event->data->object->number; // Invoice Number "number": "845D7F71-0019",

            // does this chagrge have a payment_method
            if (!empty(($payment_method = $charge->payment_method))) {
                // is there a subscription_id?
                if (
                    !empty(
                        ($subscription_id = $event->data->object->subscription)
                    )
                ) {
                    // if found, then update default_payment_method to the payment method that just succeeded for charge
                    // call stripe function
                    try {
                        $stripe->subscriptions->update($subscription_id, [
                            "default_payment_method" => $payment_method,
                        ]);
                    } catch (Exception $e) {
                        // Continue processing even if this fails
                        // This can happen with test clocks or other edge cases
                    }
                }
            }

            // INSERT Payment
            if (!empty($donation["id"])) {
                $data = [
                    "donation_id" => $donation["id"],
                    "customer_id" => $customer_id,
                    "payment_id" => $payment_id,
                    "amount" => $amount,
                    "amount_refunded" => 0.00,
                    "method" => $method,
                    "processor" => $processor,
                    "fingerprint" => $fingerprint,
                    "card_type" => $card_type,
                    "date_created" => $date_created,
                    "last4" => $last4 ? str_pad($last4, 4, '0', STR_PAD_LEFT) : null,
                    "brand" => $brand,
                    "exp_month" => $exp_month ? str_pad($exp_month, 2, '0', STR_PAD_LEFT) : null,
                    "exp_year" => $exp_year,
                    "status" => $status,
                ];

                $stmt = $db->prepare("INSERT IGNORE INTO `payments`
                                (donation_id, customer_id, payment_id, amount, amount_refunded, method, processor, fingerprint, card_type, date_created, last4, brand, exp_month, exp_year, `status`)
                                VALUES
                                (:donation_id, :customer_id, :payment_id, :amount, :amount_refunded, :method, :processor, :fingerprint, :card_type, FROM_UNIXTIME(:date_created), :last4, :brand, :exp_month, :exp_year, :status)");
                $stmt->execute($data);

                // success
                $last_id = $db->lastInsertId();

                if ($last_id > 0) {
                    http_response_code(200); // success
                    // Read the payment and send as JSON
                    echo json_encode(
                        (new Payment($db))
                            ->readStripePayment($event->data->object->charge)
                            ->fetch(PDO::FETCH_ASSOC),
                        JSON_NUMERIC_CHECK,
                    );

                    // Send email if payment was successful & email exists in DB
                    if (!empty($donation["email"])) {
                        // Check to see if this is 1st payment, check last 4 characters == 0001
                        if (substr($invoice_number, -4) == "0001") {
                            // "number": "123a1b12-0019",
                            (new Email($db))->sendMessage(
                                "thankyou",
                                $transaction_id,
                            ); // call sendMessage function from Email Class
                        }
                        // if not 1st payment, don't send email
                    }
                } else {
                    // http failure
                    http_response_code(500);
                    throw new Exception(
                        "PAYMENTS INSERT (stripe-webhook) FAILED couldn't insert record for $customer_id",
                    );
                }
            } else {
                http_response_code(500);
                throw new Exception(
                    "[donation_id] could not be found for customer_id: $customer_id using transaction_id: $transaction_id",
                );
            }
        } else {
            http_response_code(404); // not found
            throw new Exception(
                "[transaction_id] could not be found for customer_id: $customer_id",
            );
            exit();
        }
    }

    // --------------------------- [invoice.updated] --------------------------- //
    if ($event->type == "invoice.updated") {
        include "events/invoice/updated.php";
    }

    // --------------------------- [invoice.voided] --------------------------- //
    if ($event->type == "invoice.voided") {
        if ($event->data->object->billing_reason == "subscription_create") {
            // subscription_create indicates an invoice created due to creating a subscription.
            if (
                !empty(
                    $event->data->object->lines->data[0]->metadata
                        ->transaction_id
                )
            ) {
                $transaction_id =
                    $event->data->object->lines->data[0]->metadata
                        ->transaction_id; //StationAdmin transaction_id

                // lookup subscription
                $subscription = (new Donation($db))
                    ->readSubscriptionsByTransaction_id($transaction_id)
                    ->fetch(PDO::FETCH_ASSOC);

                // is there a subscription_id?
                if (!empty($subscription["subscription_id"])) {
                    // delete entry in DB for subscription_id
                    (new Subscription($db))->delete(
                        $subscription["subscription_id"],
                    );
                }

                // lookup donation_id
                $donation = (new Donation($db))
                    ->readDonationByTransactionID($transaction_id)
                    ->fetch(PDO::FETCH_ASSOC);

                // call donation delete function
                (new Donation($db))->delete($donation["id"]);

                // success
                http_response_code(200); // success
                echo json_encode([
                    "message" =>
                        "Donation " . $donation["id"] . " was deleted.",
                ]);
                exit();
            }
            http_response_code(404); // not found
            echo json_encode(["message" => "[transaction_id] not found"]);
            exit();
        }
        http_response_code(304); // not modified
        echo json_encode([
            "message" =>
                "Nothing was changed because Billing reason:" .
                $event->data->object->billing_reason,
        ]);
        exit();
    }

    // --------------------------- [price.created] --------------------------- //
    if ($event->type == "price.created") {
        $price_id = $event->data->object->id; //  VARCHAR(255)
        $amount = $event->data->object->unit_amount; //  int(11)
        $product_id = $event->data->object->product; //  VARCHAR(255)
        // INSERT  into table

        $data = [
            "id" => $price_id,
            "amount" => $amount,
            "product_id" => $product_id,
        ];

        $stmt = $db->prepare("INSERT IGNORE INTO `stripe_prices`
                            (`id`, `amount`, `product_id`)
                            VALUES
                            (:id, :amount, :product_id)");
        $stmt->execute($data);

        // success
        http_response_code(200); // success
        echo json_encode([
            "message" => "New record ($price_id) created successfully.",
        ]);
        exit();
    }

    // --------------------------- [price.deleted] --------------------------- //
    if ($event->type == "price.deleted") {
        $price_id = $event->data->object->id; //  VARCHAR(255)
        // DROP table

        $data = [
            "id" => $price_id,
        ];
        $stmt = $db->prepare("DELETE FROM `stripe_prices`
                            WHERE
                            `stripe_prices`.`id` =:id");
        $stmt->execute($data);

        // success
        http_response_code(200); // success
        echo json_encode([
            "message" => "Record ($price_id) removed successfully.",
        ]);
        exit();
    }

    // catch exceptions
} catch (\UnexpectedValueException $e) {
    // Invalid payload
    http_response_code(400);
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage(),
    ]);
} catch (\Stripe\Error\SignatureVerification $e) {
    // Invalid signature
    http_response_code(400);
    echo json_encode([
        "status" => "error",
        "message" => $e->getMessage(),
    ]);
} catch (PDOException $e) {
    // DB Error
    echo json_encode([
        "code" => $e->getCode(),
        "message" => $stmt->errorInfo()[2],
    ]);
} catch (Exception $e) {
    echo json_encode([
        "message" => $e->getMessage(),
    ]);
    error_log($e->getMessage(), 0);
    // Check if we're in livemode or testmode
    if (
        !empty($event->data->object->livemode) ||
        !empty($event->data->object->lines->data[0]->livemode)
    ) {
        mail(
            "$admin_email",
            "UPDATE DONOR (stripe-webhook) ERROR:",
            $e->getMessage(),
        );
    }
}
