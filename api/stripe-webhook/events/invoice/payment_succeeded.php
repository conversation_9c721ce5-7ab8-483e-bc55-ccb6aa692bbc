<?php

/**
 * Stripe Webhook Handler: invoice.payment_succeeded
 *
 * Processes successful invoice payments for subscription first payments.
 * Handles premium quantity adjustments for subscription create events.
 *
 * @param object $event Stripe webhook event object
 * @param PDO $db Database connection
 */

require_once __DIR__ . "/../../utils/handle_premium_adjustments.php";

try {
    // Log webhook processing start
    error_log("[invoice.payment_succeeded] Processing invoice: " . ($event->data->object->id ?? 'unknown'));

    // Validate required event data
    if (empty($event->data->object)) {
        throw new Exception("Invalid webhook event: missing event data object");
    }

    $object = $event->data->object;
    $invoice_id = $object->id ?? null;

    if (empty($invoice_id)) {
        throw new Exception("Invalid webhook event: missing invoice ID");
    }

    // Check if the payment is for the first subscription payment
    $billing_reason = $object->billing_reason ?? null;
    if ($billing_reason !== "subscription_create") {
        error_log("[invoice.payment_succeeded] Skipping non-subscription-create invoice: $invoice_id (billing_reason: $billing_reason)");

        http_response_code(200);
        echo json_encode([
            "message" => "Skipped non-subscription-create invoice",
            "invoice_id" => $invoice_id,
            "billing_reason" => $billing_reason
        ]);
        exit();
    }

    // Validate invoice lines data structure
    if (empty($object->lines->data) || !is_array($object->lines->data) || empty($object->lines->data[0])) {
        throw new Exception("Invalid invoice structure: missing or empty lines data");
    }

    // Retrieve transaction_id from metadata
    $transaction_id = $object->lines->data[0]->metadata->transaction_id ?? null;
    if (!$transaction_id) {
        throw new Exception("Transaction ID not found in invoice line metadata");
    }

    error_log("[invoice.payment_succeeded] Processing subscription create for transaction: $transaction_id");

    // Fetch premium IDs using the new method
    $premium_ids = (new Donation($db))->getPremiumIDsByTransactionID($transaction_id);

    if (empty($premium_ids)) {
        error_log("[invoice.payment_succeeded] No premiums found for transaction: $transaction_id");

        http_response_code(200);
        echo json_encode([
            "message" => "No premiums found for transaction",
            "transaction_id" => $transaction_id,
            "invoice_id" => $invoice_id
        ]);
        exit();
    }

    // Check if premiums were already processed
    $premiums_processed = $object->lines->data[0]->metadata->premiums_processed ?? false;

    if (!$premiums_processed) {
        error_log("[invoice.payment_succeeded] Processing premium adjustments for transaction: $transaction_id");
        handlePremiumAdjustmentsFromStripe($db, $premium_ids);
        error_log("[invoice.payment_succeeded] Premium adjustments completed for transaction: $transaction_id");
    } else {
        error_log("[invoice.payment_succeeded] Premiums already processed for transaction: $transaction_id");
    }

    // Success response
    http_response_code(200);
    echo json_encode([
        "message" => "Invoice payment succeeded processed successfully",
        "invoice_id" => $invoice_id,
        "transaction_id" => $transaction_id,
        "premium_count" => count($premium_ids),
        "premiums_processed" => !empty($premiums_processed)
    ]);

} catch (Exception $e) {
    error_log("[invoice.payment_succeeded] Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "error" => "Webhook processing failed",
        "message" => $e->getMessage(),
        "invoice_id" => $invoice_id ?? null
    ]);
}
