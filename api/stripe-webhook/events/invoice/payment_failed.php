<?php

/**
 * Stripe Webhook Handler: invoice.payment_failed
 *
 * Processes failed invoice payments for subscription payments.
 * Sends notification email to staff and logs failure details.
 *
 * @param object $event Stripe webhook event object
 * @param PDO $db Database connection
 */

try {
    // Log webhook processing start
    error_log("[invoice.payment_failed] Processing failed invoice: " . ($event->data->object->id ?? 'unknown'));

    // Validate required event data
    if (empty($event->data->object)) {
        throw new Exception("Invalid webhook event: missing event data object");
    }

    $invoice = $event->data->object;
    $invoice_id = $invoice->id ?? null;

    if (empty($invoice_id)) {
        throw new Exception("Invalid webhook event: missing invoice ID");
    }

    // Extract basic invoice information safely
    $customer_id = $invoice->customer ?? null;
    $customer_name = $invoice->customer_name ?? 'Unknown Customer';
    $hosted_invoice_url = $invoice->hosted_invoice_url ?? null;

    // Validate invoice lines data structure
    if (empty($invoice->lines->data) || !is_array($invoice->lines->data) || empty($invoice->lines->data[0])) {
        error_log("[invoice.payment_failed] Warning: Missing or empty lines data for invoice: $invoice_id");
        $transaction_id = null;
        $donor_id = null;
        $description = 'No line items available';
    } else {
        // Extract metadata from first line item
        $line_item = $invoice->lines->data[0];
        $transaction_id = $line_item->metadata->transaction_id ?? null;
        $donor_id = $line_item->metadata->donor_id ?? null;
        $description = $line_item->description ?? 'No description available';
    }

    error_log("[invoice.payment_failed] Failed payment for customer: $customer_id, transaction: $transaction_id");

    // Prepare email notification
    $email_data = [
        'customer_id' => $customer_id,
        'transaction_id' => $transaction_id,
        'invoice_id' => $invoice_id,
        'donor_id' => $donor_id,
        'customer_name' => $customer_name,
        'description' => $description,
        'hosted_invoice_url' => $hosted_invoice_url
    ];

    // Build email message
    $msg = "[customer_id]: $customer_id\n";
    $msg .= "[transaction_id]: $transaction_id\n";
    $msg .= "[invoice_id]: $invoice_id\n";
    $msg .= "[donor_id]: $donor_id\n";
    $msg .= "[customer_name]: $customer_name\n";
    $msg .= "[description]: $description\n";
    $msg .= "[hosted_invoice_url]: $hosted_invoice_url";

    // Use wordwrap() if lines are longer than 70 characters
    $msg = wordwrap($msg, 70);

    // Send email notification
    try {
        $email_sent = mail(
            "<EMAIL>",
            "[Stripe] Invoice Payment Failed for: $customer_name",
            $msg
        );

        if ($email_sent) {
            error_log("[invoice.payment_failed] Notification email sent for invoice: $invoice_id");
        } else {
            error_log("[invoice.payment_failed] Failed to send notification email for invoice: $invoice_id");
        }
    } catch (Exception $e) {
        error_log("[invoice.payment_failed] Email sending error for invoice $invoice_id: " . $e->getMessage());
        // Don't fail the webhook for email issues
    }

    // Success response
    http_response_code(200);
    echo json_encode([
        "message" => "Invoice payment failed processed successfully",
        "invoice_id" => $invoice_id,
        "customer_id" => $customer_id,
        "transaction_id" => $transaction_id,
        "email_sent" => $email_sent ?? false,
        "notification_sent_to" => "<EMAIL>"
    ]);

} catch (Exception $e) {
    error_log("[invoice.payment_failed] Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "error" => "Webhook processing failed",
        "message" => $e->getMessage(),
        "invoice_id" => $invoice_id ?? null
    ]);
    exit();
}
