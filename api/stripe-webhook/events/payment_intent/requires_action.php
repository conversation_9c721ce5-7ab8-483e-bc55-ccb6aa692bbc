<?php
require_once __DIR__ . "/../../utils/create_pending_payment.php";
global $stripe;

try {
    /*
     *
     * We exclude monthly/subscription related payments from this because currently, our API does not
     * provide a transaction_id in the Stripe metadata when creating subscription payment_intents.
     * This could be fixed, and then we could remove our current implementation of
     * the invoice.payment_action_required event handler, to handle both cases from this file,
     * but this works - the payment_action_required handler is simply a thin wrapper around createPendingPayment.
     *
     */

    $has_invoice = isset($event->data->object->invoice);

    // Check if the event is for microdeposits
    $microdeposit =
        $event->data->object->next_action->type ===
            "verify_with_microdeposits" ??
        null;

    if (!$microdeposit || $has_invoice) {
        http_response_code(200);
        echo json_encode([
            "message" =>
                "The 'payment_intent.requires_action' event is only implemented for One-Time ACH payments using microdeposit verification. Monthly payments/subscriptions use the 'invoice.payment_action_required' event to process pending payments.",
        ]);
        exit();
    }

    $payment = createPendingPayment($db, $stripe, $event, "payment_intent");

    http_response_code(200);
    echo json_encode($payment, JSON_NUMERIC_CHECK);
} catch (Exception $e) {
    error_log("[payment_intent.requires_action] Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "error" => "Webhook processing failed",
        "message" => $e->getMessage()
    ]);
    exit();
}
