<?php

/**
 * Stripe Webhook Handler: payment_intent.succeeded
 *
 * Processes successful payment intents for one-time payments.
 * Subscription payments should be handled by invoice.payment_succeeded webhook.
 *
 * @param object $event Stripe webhook event object
 * @param PDO $db Database connection
 */

require_once __DIR__ . "/../../utils/handle_premium_adjustments.php";

try {
    // Log webhook processing start
    error_log("[payment_intent.succeeded] Processing payment intent: " . ($event->data->object->id ?? 'unknown'));

    // Validate required event data
    if (empty($event->data->object)) {
        throw new Exception("Invalid webhook event: missing event data object");
    }

    $payment_intent = $event->data->object;
    $payment_intent_id = $payment_intent->id ?? null;

    if (empty($payment_intent_id)) {
        throw new Exception("Invalid webhook event: missing payment intent ID");
    }

    // Check if the payment is related to a subscription via the `invoice` field
    $invoice_id = $payment_intent->invoice ?? null;

    if ($invoice_id) {
        error_log("[payment_intent.succeeded] Skipping subscription-related payment intent: $payment_intent_id (invoice: $invoice_id)");

        http_response_code(200);
        echo json_encode([
            "message" => "Skipped subscription-related payment",
            "payment_intent_id" => $payment_intent_id,
            "invoice_id" => $invoice_id
        ]);
        exit();
    }

    // Validate charges data structure
    if (empty($payment_intent->charges->data) || !is_array($payment_intent->charges->data) || empty($payment_intent->charges->data[0])) {
        throw new Exception("Invalid payment intent structure: missing or empty charges data");
    }

    // Check if the payment already exists
    $payment_id = $payment_intent->charges->data[0]->id;
    $existingPayment = (new Payment($db))->readStripePayment($payment_id);

    if ($existingPayment->rowCount() > 0) {
        $paymentData = $existingPayment->fetch(PDO::FETCH_ASSOC);
        error_log("[payment_intent.succeeded] Payment already exists for charge: $payment_id");

        http_response_code(200);
        echo json_encode($paymentData, JSON_NUMERIC_CHECK);
        exit();
    }

    // Extract charge data safely
    $charge = $payment_intent->charges->data[0];
    $customer_id = $charge->customer ?? null;

    // Retrieve transaction_id from metadata
    $transaction_id = $charge->metadata->transaction_id ?? null;

    // Handle missing transaction_id
    if (!$transaction_id) {
        error_log("[payment_intent.succeeded] Transaction ID missing in metadata for Payment ID: $payment_id");
        throw new Exception("Transaction ID not found in charge metadata");
    }

    error_log("[payment_intent.succeeded] Processing one-time payment for transaction: $transaction_id");

    // Validate amount
    $amount_cents = $charge->amount ?? 0;
    if ($amount_cents <= 0) {
        throw new Exception("Invalid charge amount: $amount_cents");
    }
    $amount = $amount_cents / 100; // Amount in dollars

    // Retrieve Premium metadata
    $premium_metadata = $charge->metadata->Premium ?? "[]";

    // Decode Premium metadata (assumes JSON string)
    $premium_ids = json_decode($premium_metadata, true);

    // Ensure `premium_ids` is an array
    if (!is_array($premium_ids)) {
        error_log("[payment_intent.succeeded] Invalid Premium metadata. Defaulting to an empty array. Metadata: $premium_metadata");
        $premium_ids = []; // Default to an empty array if decoding failed
    }

    // Retrieve the associated donation
    $donation = (new Donation($db))->readDonationByTransactionID($transaction_id)->fetch(PDO::FETCH_ASSOC);
    if (!$donation) {
        error_log("[payment_intent.succeeded] Donation not found for Transaction ID: $transaction_id");
        throw new Exception("Donation not found for transaction ID: $transaction_id");
    }

    // Process premium adjustments if not already processed
    $premiums_processed = $payment_intent->metadata->premiums_processed ?? false;

    if (!$premiums_processed && !empty($premium_ids)) {
        error_log("[payment_intent.succeeded] Processing premium adjustments for transaction: $transaction_id");
        handlePremiumAdjustmentsFromStripe($db, $premium_ids);
        error_log("[payment_intent.succeeded] Premium adjustments completed for transaction: $transaction_id");
    } else if ($premiums_processed) {
        error_log("[payment_intent.succeeded] Premiums already processed for transaction: $transaction_id");
    } else {
        error_log("[payment_intent.succeeded] No premiums to process for transaction: $transaction_id");
    }

    // Success response with detailed information
    http_response_code(200);
    echo json_encode([
        "message" => "Payment intent succeeded processed successfully",
        "payment_intent_id" => $payment_intent_id,
        "charge_id" => $payment_id,
        "transaction_id" => $transaction_id,
        "amount" => $amount,
        "premium_count" => count($premium_ids),
        "premiums_processed" => !empty($premiums_processed)
    ], JSON_NUMERIC_CHECK);

} catch (Exception $e) {
    error_log("[payment_intent.succeeded] Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "error" => "Webhook processing failed",
        "message" => $e->getMessage(),
        "payment_intent_id" => $payment_intent_id ?? null
    ]);
    exit();
}
