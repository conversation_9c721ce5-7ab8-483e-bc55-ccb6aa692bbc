<?php

/**
 * Stripe Webhook Handler: charge.updated
 *
 * Processes charge update events, typically for description or metadata changes.
 * This webhook is informational and doesn't usually require database updates.
 *
 * @param object $event Stripe webhook event object
 * @param PDO $db Database connection
 */

require_once __DIR__ . "/../../utils/handle_ach_status.php";

try {
    // Log webhook processing start
    error_log("[charge.updated] Processing charge update: " . ($event->data->object->id ?? 'unknown'));

    // Validate required event data
    if (empty($event->data->object)) {
        throw new Exception("Invalid webhook event: missing event data object");
    }

    $charge = $event->data->object;
    $charge_id = $charge->id ?? null;

    if (empty($charge_id)) {
        throw new Exception("Invalid webhook event: missing charge ID");
    }

    // Handle ACH status updates first
    if (handle_ach_status($event, $db)) {
        error_log("[charge.updated] ACH status handled for charge: $charge_id");
        exit();
    }

    // Check if this is a payment we track
    $existingPayment = (new Payment($db))->readStripePayment($charge_id);
    
    if ($existingPayment->rowCount() > 0) {
        $paymentData = $existingPayment->fetch(PDO::FETCH_ASSOC);
        error_log("[charge.updated] Found existing payment for charge: $charge_id");

        // Log what was updated (for debugging)
        $previous_attributes = $event->data->previous_attributes ?? null;
        if ($previous_attributes) {
            $updated_fields = array_keys((array)$previous_attributes);
            error_log("[charge.updated] Updated fields for charge $charge_id: " . implode(', ', $updated_fields));
        }

        // Return existing payment data
        http_response_code(200);
        echo json_encode([
            "message" => "Charge update processed successfully",
            "charge_id" => $charge_id,
            "payment_id" => $paymentData['id'],
            "updated_fields" => $updated_fields ?? []
        ], JSON_NUMERIC_CHECK);
        
    } else {
        // Charge not found in our system - this is normal for charges we don't track
        error_log("[charge.updated] Charge not found in our system: $charge_id");
        
        http_response_code(200);
        echo json_encode([
            "message" => "Charge update acknowledged (not tracked)",
            "charge_id" => $charge_id
        ]);
    }

} catch (Exception $e) {
    error_log("[charge.updated] Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "error" => "Webhook processing failed",
        "message" => $e->getMessage(),
        "charge_id" => $charge_id ?? null
    ]);
}
