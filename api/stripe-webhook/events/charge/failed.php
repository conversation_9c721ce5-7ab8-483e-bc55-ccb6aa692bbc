<?php

/**
 * Stripe Webhook Handler: charge.failed
 *
 * Processes failed charge events for one-time payments.
 * Handles ACH status updates and logs failure details.
 *
 * @param object $event Stripe webhook event object
 * @param PDO $db Database connection
 */

require_once __DIR__ . "/../../utils/handle_ach_status.php";

try {
    // Log webhook processing start
    error_log("[charge.failed] Processing failed charge: " . ($event->data->object->id ?? 'unknown'));

    // Validate required event data
    if (empty($event->data->object)) {
        throw new Exception("Invalid webhook event: missing event data object");
    }

    $charge = $event->data->object;
    $charge_id = $charge->id ?? null;

    if (empty($charge_id)) {
        throw new Exception("Invalid webhook event: missing charge ID");
    }

    // Handle ACH status updates first
    if (handle_ach_status($event, $db)) {
        error_log("[charge.failed] ACH status handled for charge: $charge_id");
        exit();
    }

    // Extract failure information
    $failure_code = $charge->failure_code ?? null;
    $failure_message = $charge->failure_message ?? null;
    $outcome = $charge->outcome ?? null;
    $customer_id = $charge->customer ?? null;
    $amount = isset($charge->amount) ? ($charge->amount / 100) : null;

    // Extract transaction ID from metadata if available
    $transaction_id = $charge->metadata->transaction_id ?? null;

    error_log("[charge.failed] Charge failed - ID: $charge_id, Code: $failure_code, Message: $failure_message");

    // Check if this is a payment we track
    $existingPayment = (new Payment($db))->readStripePayment($charge_id);

    if ($existingPayment->rowCount() > 0) {
        $paymentData = $existingPayment->fetch(PDO::FETCH_ASSOC);
        error_log("[charge.failed] Found existing payment for failed charge: $charge_id");

        // Log detailed failure information
        if ($outcome) {
            $network_status = $outcome->network_status ?? 'unknown';
            $reason = $outcome->reason ?? 'unknown';
            $seller_message = $outcome->seller_message ?? 'No seller message';
            $type = $outcome->type ?? 'unknown';

            error_log("[charge.failed] Failure details - Network: $network_status, Reason: $reason, Type: $type");
        }

        // Return payment data with failure information
        http_response_code(200);
        echo json_encode([
            "message" => "Charge failure processed successfully",
            "charge_id" => $charge_id,
            "payment_id" => $paymentData['id'],
            "transaction_id" => $transaction_id,
            "failure_code" => $failure_code,
            "failure_message" => $failure_message,
            "amount" => $amount,
            "customer_id" => $customer_id
        ], JSON_NUMERIC_CHECK);

    } else {
        // Charge not found in our system - this could be normal for some failed charges
        error_log("[charge.failed] Failed charge not found in our system: $charge_id");

        http_response_code(200);
        echo json_encode([
            "message" => "Charge failure acknowledged (not tracked)",
            "charge_id" => $charge_id,
            "failure_code" => $failure_code,
            "failure_message" => $failure_message,
            "transaction_id" => $transaction_id,
            "amount" => $amount
        ], JSON_NUMERIC_CHECK);
    }

} catch (Exception $e) {
    error_log("[charge.failed] Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        "error" => "Webhook processing failed",
        "message" => $e->getMessage(),
        "charge_id" => $charge_id ?? null
    ]);
    exit();
}
