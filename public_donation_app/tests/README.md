# KPFA Public Donation App - E2E Testing

This directory contains end-to-end tests for the KPFA Public Donation App using Playwright.

## todo: add cases to assist Link payment testng

## Setup

1. Install dependencies: `npm install`
2. Install Playwright browsers: `npx playwright install`
3. Start the donation app: `npm start` (must be running on localhost:3000)

## Running Tests

### Automated Tests (No Manual Interaction)

```bash
# Run all automated tests (recommended for CI/development)
npm run test:auto

# Run automated tests with UI for debugging
npm run test:auto:ui
```

### Manual Tests (Interactive Payment Testing)

**Important**: Manual tests require a visible browser and run **one at a time** for manual completion.

```bash
# Run all manual tests sequentially (one at a time)
npm run test:manual

# Run all manual tests with UI (best for debugging)
npm run test:manual:ui

# Run specific manual tests (individual)
npm run test:manual:cc          # One-time credit card
npm run test:manual:monthly     # Monthly credit card subscription
npm run test:manual:ach         # One-time ACH payment
npm run test:manual:ach-monthly # Monthly ACH subscription
```

### Advanced Usage

```bash
# Run specific automated test
npx playwright test --grep "should verify Stripe Elements integration"

# Run with different browser
npx playwright test --project=firefox
```

## Test Categories

### Automated Tests

- ✅ **Stripe Elements Integration** - Verifies Stripe loads and payment modal works
- ✅ **Monthly Subscription Setup** - Tests subscription creation flow
- ✅ **Form Validation** - Tests required field validation
- ✅ **Smoke Tests** - Basic app functionality verification

### Manual Tests (Require Browser Interaction)

- 🔄 **MANUAL: One-Time Credit Card Payment** - Basic credit card donation
- 🔄 **MANUAL: Monthly Subscription Credit Card** - Recurring credit card subscription
- 🔄 **MANUAL: ACH Bank Payment** - One-time ACH bank transfer
- 🔄 **MANUAL: Monthly ACH Subscription** - Recurring ACH subscription
- ⏸️ **MANUAL: Google Pay & Apple Pay** - Digital wallet payments (disabled for now)

## How Manual Tests Work

1. **Start donation app**: `npm start` (localhost:3000)
2. **Run manual test**: `npx playwright test --grep "MANUAL: ACH" --headed --project=chromium`
3. **Browser opens**: Test fills form automatically, pauses at payment step
4. **Manual interaction**: Complete payment in browser using provided test data
5. **Continue**: Click "Continue" in browser to finish test

### Test Data for Manual Tests

**Credit Cards:**

- `****************` - Visa (succeeds)
- `****************` - Visa (declined)

**ACH Bank Details:**

- Routing: `*********`
- Account: `************`
- Type: Checking

## Safety Features

All tests include safety verification to ensure they run in test mode only. Tests will fail if not running against the test environment.

## Troubleshooting

**Manual tests not showing browser:**

- Use `npm run test:manual` (recommended)
- Or use UI mode: `npm run test:manual:ui`

**Tests failing with "Please use a real phone number":**

- Tests use valid 555-format phone numbers that should pass validation
- If issues persist, check phone validation rules in the backend

**ACH tests failing:**

- Ensure using exact button selectors: `$ 50` vs `$ 500` conflict resolved
- Monthly tests use `$ 50/mo` format

## Configuration

Tests are configured in `../playwright.config.ts` and run against `http://localhost:3000` by default.
